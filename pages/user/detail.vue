<script>
import CTooltip from "../../components/cTooltip/index.vue";
import OrderTable from "./component/order-table.vue";
import CouponList from "./component/coupon-list.vue";
import ExchangeList from "./component/exchange-list.vue";
import StoredValue from "./component/stored-value.vue";
import UserService from "../../api/user";
import {format_time} from "../../utils/helper";
import CLoading from "../../components/cLoading/loading.vue";

export default {
  name: 'userDetail',
  components: {
    CLoading,
    StoredValue,
    ExchangeList,
    CouponList,
    OrderTable,
    CTooltip

  },
  props: {},
  data() {
    return {
      activeTab: '0',
      scrollTop: 0,
      tabs: [
        { title: '订单', key: '1' },
        { title: '卡券', key: '2' },
        { title: '通兑券', key: '3' },
        { title: '储值', key: '4' },
      ],
      detail: {},
      showLoading: false
    }
  },
  created() {
    this.scrollTop = 0;
  },
  mounted() {
    const query = this.$store.getters['global/getQuery'];
    this.activeTab = query?.tabKey || '1';
  },
  computed: {
    getUserAddress() {
      const { prov, city, county, other } = this.detail?.address || {};
      return (prov?.name || '') + (city?.name || '') + (county?.name || '') + (other || '');
    }
  },
  watch: {
    '$store.state.global.query.id': {
      immediate: true,
      handler(id) {
        this.getUserDetail(id)
      }
    }
  },
  methods: {
    format_time,
    backPage() {
      // 设置路由参数
      this.$store.commit('global/SET_QUERY', {})
      // 回到列表页
      this.$emit('changePageTab', 'list')
    },
    changeDetailTab(tab) {
      this.activeTab = tab
    },
    getUserDetail(id) {
      if (!id) return
      this.showLoading = true;
      UserService.getUserDetail({
        uid:id
      }).then((res) => {
        this.detail = res?.user || {}
      }).finally(() => {
        this.showLoading = false;
      })
    },
  }
}
</script>

<template>
  <view class="page-container">
    <view class="base-detail">
      <view class="base-detail-action">
        <view class="back-page">
          <image class="back-img" src="@/static/image/user/left.png" mode="aspectFit"></image>
          <view class="back-text" @click="backPage">返回</view>
        </view>
        <image class="base-detail-edit" src="@/static/image/user/edit.png" mode="aspectFit"></image>
      </view>
      <scroll-view
        class="base-detail-scroll"
        :scroll-top="scrollTop"
        scroll-y="true"
        @touchmove.stop
      >
        <view class="user-header">
          <image class="user-avatar" :src="detail.avatar" mode="aspectFit"></image>
          <view class="user-name-wrap">
            <view class="user-name">
              <view class="name" v-if="detail.real_name">
                <c-tooltip :text="detail.real_name"></c-tooltip>
              </view>
              <image v-if="+detail.auth_status === 2" class="async" src="/static/image/user/async.png"></image>
            </view>
            <view class="user-age-wrap">
              <view class="user-gender">{{ detail.sex_text || '-' }}</view>
              <view class="user-line"></view>
              <view class="user-age">{{ detail.age || '-' }}岁</view>
            </view>
          </view>
        </view>
        <view class="user-tags">
          <view class="tags" v-for="tag in detail.label_list" :key="tag.id">
            {{ tag.tag_name || '-' }}
          </view>
        </view>
        <view class="base-info">
          <view class="base-info-item">
            <view class="item-label">联系电话</view>
            <view class="item-text">{{ detail.mobile || '-' }}</view>
          </view>
          <view class="base-info-item">
            <view class="item-label">出生日期</view>
            <view class="item-text">{{ detail.birthday || '-' }}</view>
          </view>
          <view class="base-info-item">
            <view class="item-label">身份证号</view>
            <view class="item-text">{{ detail.card_no || '-' }}</view>
          </view>
          <view class="base-info-item">
            <view class="item-label">注册名称</view>
            <view class="item-text">{{ detail.nickname || '-' }}</view>
          </view>
          <view class="base-info-item">
            <view class="item-label">注册时间</view>
            <view class="item-text">{{ format_time(detail.create_time) || '-' }}</view>
          </view>
          <view class="base-info-item">
            <view class="item-label">用户来源</view>
            <view class="item-text">{{ detail.source_text || '-' }}</view>
          </view>
          <view class="base-info-item">
            <view class="item-label">联系地址</view>
            <view class="item-text">{{ getUserAddress || '-' }}</view>
          </view>
<!--          <view class="base-info-item">-->
<!--            <view class="item-label">会员名称</view>-->
<!--            <view class="item-text">980榕益健康会员、9800粉丝金钻会员</view>-->
<!--          </view>-->
        </view>
      </scroll-view>
    </view>
    <view class="list-wrap">
      <view class="header-tabs">
        <view class="tabs">
          <view
            :class="['tabs-item', item.key === activeTab && 'active']"
            v-for="item in tabs"
            :key="item.key"
            @click="changeDetailTab(item.key)"
          >
            {{ item.title }}
          </view>
        </view>
      </view>
      <view class="table-wrap">
        <view class="table-content" >
          <order-table ref="orderTable" v-if="activeTab === '1'" />
          <coupon-list ref="couponList" v-if="activeTab === '2'" />
          <exchange-list ref="exchangeList" v-if="activeTab === '3'" />
          <stored-value v-if="activeTab === '4'" />
        </view>
      </view>
    </view>
<!--    <c-loading :show="showLoading" />-->
  </view>
</template>

<style scoped lang="scss">
.page-container {
  width: 100%;
  height: 100vh;
  display: flex;
  .base-detail {
    width: 480rpx;
    height: 100vh;
    background: #FFFFFF;
    box-shadow: 4rpx 0 12rpx 0 rgba(20,21,22,0.06);
    padding-top: 40rpx;
    padding-bottom: 20rpx;
    flex-shrink: 0;
    z-index: 1;
    .base-detail-action {
      width: 100%;
      display: flex;
      align-items: center;
      .back-page {
        width: 136rpx;
        height: 56rpx;
        padding-right: 8rpx;
        background: rgba(21,91,212,0.08);
        border-radius: 0 28rpx 28rpx 0;
        margin-right: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        .back-img {
          width: 28rpx;
          height: 26rpx;
          margin-right: 4rpx;
        }
        .back-text {
          font-size: 24rpx;
          color: #155BD4;
        }
      }
      .base-detail-edit {
        width: 28rpx;
        height: 28rpx;
        margin-right: 32rpx;
      }
    }
    .base-detail-scroll {
      width: calc(100% - 64rpx);
      height: 100%;
      padding: 40rpx 32rpx;
      .user-header {
        width: 100%;
        display: flex;
        align-items: center;
        .user-avatar {
          width: 96rpx;
          height: 96rpx;
          border-radius: 64rpx;
          margin-right: 16rpx;
          flex-shrink: 0;
        }
        .user-name-wrap {
          width: calc(100% - 116rpx);
          .user-name {
            width: 100%;
            display: flex;
            align-items: center;
            .name {
              max-width: 240rpx;
              font-weight: 600;
              font-size: 32rpx;
              color: #333333;
              padding-right: 4rpx;
            }
            .async {
              width: 32rpx;
              height: 32rpx;
            }
          }
          .user-age-wrap {
            width: 100%;
            display: flex;
            align-items: center;
            font-size: 24rpx;
            color: #999999;
            margin-top: 8rpx;
            .user-line {
              width: 1px;
              height: 24rpx;
              background: #cccccc;
              margin: 0 8rpx;
            }
          }
        }
      }
      .user-tags {
        width: fit-content;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 20rpx;
        .tags {
          flex-shrink: 0;
          padding: 0 8rpx;
          border: 1px solid #DCDFE6;
          font-size: 22rpx;
          color: #666666;
          line-height: 32rpx;
          text-align: center;
          margin-right: 8rpx;
        }
      }
      .base-info {
        padding-top: 48rpx;
        width: 100%;
        .base-info-item {
          width: 100%;
          margin-bottom: 30rpx;
          .item-label {
            font-size: 24rpx;
            color: #999999;
            line-height: 36rpx;
          }
          .item-text {
            margin-top: 10rpx;
            font-size: 24rpx;
            color: #333333;
            line-height: 36rpx;
            white-space: wrap;
          }
        }
      }
    }
  }
  .list-wrap {
    flex: 1;
    height: 100vh;
    overflow: hidden;
    .header-tabs {
      width: 100%;
      height: 104rpx;
      padding-top: 8rpx;
      display: flex;
      justify-content: center;
      background: #FFFFFF;
      .tabs {
        width: fit-content;
        height: 100%;
        display: flex;
        align-items: center;
        .tabs-item {
          width: 152rpx;
          height: 96rpx;
          font-size: 28rpx;
          color: #666666;
          line-height: 112rpx;
          text-align: center;
          border-bottom: 4rpx solid #FFFFFF;
          transition: all 0.3s ease-in-out;
        }
        .tabs-item.active {
          background: linear-gradient( 180deg, rgba(21,91,212,0) 0%, rgba(21,91,212,0.06) 100%);
          border-color: #115bd4;
          font-weight: bold;
          color: #115bd4;
        }
      }
    }
    .table-wrap {
      width: 100%;
      height: calc(100% - 108rpx);
      padding: 24rpx;
      background: #F5F6F8;
      .table-content {
        width: 100%;
        height: 100%;
        background: #fff;
      }
    }
  }
}
</style>
