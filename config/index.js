// 获取当前环境
const ENV = 'TEST'
// const ENV = 'UAT'
// const ENV = 'PROD'

// #ifdef H5
// ENV = process.env.NODE_ENV === 'development' ? 'dev' : 'prod'
// #endif

// #ifdef MP-WEIXIN
// 微信小程序环境判断，可以通过开发工具的"编译模式"区分
// const accountInfo = uni.getSystemInfoSync()
// console.log('%c [ accountInfo ]-11', 'font-size:13px; background:#904216; color:#d4865a;', accountInfo)
// if (accountInfo.miniProgram.envVersion === 'develop') {
//   ENV = 'dev'
// } else if (accountInfo.miniProgram.envVersion === 'trial') {
//   ENV = 'test'
// } else {
//   ENV = 'prod'
// }
// #endif

// 环境配置
const envConfig = {
  TEST: {
    BASE_URL: 'https://clinic2sit-tnjnimgq5ndkxodk3m.rsjxx.com/api-server',
    SSE_BASE_URL: 'https://zpurchase2sit-t5kxzxf97k3blignrm.rsjxx.com/api-server/',
    UPLOAD_URL: 'https://upload.qiniup.com',
    /**
     * 接口请求签名Key
     */
    mapiSignKey: 'qOOx00Op3',
    STORAGE_KEY: 'TEST-FAPP-HIS',
  },
  UAT: {
    BASE_URL: 'https://clinic2uat-tntu5zjfmzda0zdnhm.rsjxx.com/api-server',
    SSE_BASE_URL: 'https://zpurchase2uat-t8dqzihxyam47cgdxm.rsjxx.com/api-server/',
    UPLOAD_URL: 'https://upload.qiniup.com',
    /**
     * 接口请求签名Key
     */
    mapiSignKey: 'qOOx00Op3',
    STORAGE_KEY: 'UAT-FAPP-HIS',
  },
  DEV: {
    BASE_URL: 'https://clinic2dev-tnjnimgq5ndkxodk3m.rsjxx.com/api-server',
    SSE_BASE_URL: 'https://zpurchase2sit-t5kxzxf97k3blignrm.rsjxx.com/api-server/',
    UPLOAD_URL: 'https://upload.qiniup.com',
    /**
     * 接口请求签名Key
     */
    mapiSignKey: 'qOOx00Op3',
    STORAGE_KEY: 'TEST-FAPP-HIS',
  },
  PROD: {
    BASE_URL: 'https://mapp.rsjxx.com/api-server',
    SSE_BASE_URL: 'https://mapp.rsjxx.com/sse-server/',
    UPLOAD_URL: 'https://upload.qiniup.com',
    /**
     * 接口请求签名Key
     */
    mapiSignKey: '3NoMQDYX3',
    STORAGE_KEY: 'UAT-FAPP-HIS',
  },
}

// 当前环境配置
export default envConfig[ENV]
